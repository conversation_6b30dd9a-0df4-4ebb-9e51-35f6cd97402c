# 企业微信机器人配置
Robot_WEB_HOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY
Report_Robot_WEB_HOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY
Robot_WEB_HOOK_TEST=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY

# 医保
YIBAO_ROBOT_WEB_HOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY

# 医药SAAS
SAAS_ROBOT_WEB_HOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY

# 觅影
MIYING_ROBOT_WEB_HOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY

# 导诊
DAOZHEN_ROBOT_WEB_HOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY

# IMA
IMA_ROBOT_WEB_HOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_WEBHOOK_KEY
# 数据库配置
DB_USER=root
DB_PASSWORD=YOUR_DB_PASSWORD
DB_HOST=localhost
DB_PORT=3306
DB_NAME=bugs_data
WORKSPACE_IDS=YOUR_WORKSPACE_ID

# 混元API配置
HUNYUAN_API_URL=http://hunyuanapi.woa.com/openapi/v1
HUNYUAN_API_KEY=YOUR_HUNYUAN_API_KEY
HUNYUAN_MODEL=hunyuan-turbos-latest
ENABLE_DAILY_STATS_PUSH=True

# BUG批量处理跳过模式配置
# 支持两种模式：
# - 'exists': 记录存在则跳过模式（只要数据库中存在该BUG的评估记录就跳过）
# - 'passed': 记录存在且通过则跳过模式（只有当记录存在且评估通过时才跳过）
BUG_SKIP_MODE=exists

# BUG群聊通知配置
# 是否启用仅群聊通知模式（查询数据库中未通过的BUG并发送群聊通知，不执行评估）
BUG_EVALUATION_NOTIFY_ONLY=false