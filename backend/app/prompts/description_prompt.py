import json

from backend.app.config.config import JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID, YIYAO_SAAS_WORKSPACE_ID, MIYING_WORKSPACE_ID, DAOZHEN_WORKSPACE_ID, IMA_WORKSPACE_ID
import copy

def get_bug_report_standard(workspace_id) -> str:
    if workspace_id in [YIYAO_SAAS_WORKSPACE_ID]:
        return YIYAO_SAAS_BUG_STANDARD
    elif workspace_id in [MIYING_WORKSPACE_ID]:
        return MIYING_BUG_STANDARD
    elif workspace_id in [DAOZHEN_WORKSPACE_ID]:
        return DAOZHEN_BUG_STANDARD
    elif workspace_id in [IMA_WORKSPACE_ID]:
        return IMA_BUG_STANDARD
    return JIANKANG_BUG_STANDARD


JIANKANG_BUG_STANDARD = """
详细描述字段应包含以下五个结构性子字段，每个字段建议使用如下标题格式，缺一不可：
【条件】  
- 明确问题发生的环境：测试环境、正式环境、灰度环境、体验版正式环境，或说明机器人序号。
- 如果存在BUG发生的必要条件，可以在条件中填写。
✅ 示例：测试环境。
❌ 错误示例：无说明。  

【机型】  
- 明确设备型号和系统版本（不要求小版本），或说明与机型无关，all机型通用，不是兼容性问题。  
✅ 示例：iPhone 12 iOS 18。
❌ 错误示例：无说明。

【步骤】
- 描述操作过程，可以使用编号，操作对象需明确。
- 可以是单个步骤或多个步骤，只要能清楚表述问题复现过程即可。
- 有些缺陷需要在一定条件下发生，因此允许在BUG中添加前置条件相关表述。
✅ 示例：
1. 打开App首页 -> 点击"我的"。
或者：
1. 打开App首页 -> 点击“我的”。
2. 点击头像 -> 进入编辑页。
❌ 错误示例：左上角菜单栏->登录->注册->输入手机号...。

【预期结果】  
- 明确说明期望出现的界面或行为结果。  
✅ 示例：页面同步显示“已取消点赞”。  
❌ 错误示例：正常展示。  

【实际结果】  
- 明确当前系统实际行为，禁止使用“异常”、“不正常”等模糊词。  
✅ 示例：仍显示“已点赞”。  
❌ 错误示例：显示异常。  
"""

JIANKANG_POINT_BUG_STANDARD = """
1. 评分部分（dimension_scores）
    a. 格式要求：
        i. 每个维度评分范围为 `0.0 - 1.0`，必须为浮点数。
    b. 评分要求：
        i. 完整性：每缺1个字段扣0.4，最多扣至0分。
        ii. 清晰度：出现歧义描述或逻辑跳跃扣0.4。
        iii. 相关性：出现明显无关内容扣0.4。
        iv. 详细程度：
            1. 针对详细描述字段【预期结果】或【实际结果】的内容，先查看是否包含图片或者视频链接。
                a. 若仅贴视频链接没有文字，则扣0.4分。
                b. 若仅贴图片链接不包含文字则** 查看输入信息中图片链接对应的OCR解析的内容是否提到红框 **，若没有则扣0.4分。
    c. 注意事项：
        i. 只要段中含有简洁明了的文字描述（如“显示网络异常”、“按钮无反应”等），即认为已说明，无需长篇解释。只有在完全缺少文字，仅附图时，才视为“无说明”，不需要充分的文字说明!!
        ii. 若某项评分低于0.7，将触发人工复审，请确保低分有合理依据!! 

2. 反馈部分（feedback）
    a. 格式要求：
        i. 所有纬度按照格式：`维度名 -扣分值：扣分原因`
    b. 注意事项：
        i. 如果某个纬度为满分，扣分为0，那么则不需要展示该纬度的反馈。
        ii. 扣分值需要和dimension_scores中的实际得分相匹配，例如完整性扣0.4分那么完整性得分为1 - 0.4 = 0.6分
"""

JIANKANG_OPTIMIZATION_STANDARD = """
1. 问题字段优化:
    a. 你需要参考BUG规范模版中需要填写的字段，仅需针对上一步评分反馈结果（stage1_json）中扣分原因** 有问题的字段 **进行优化!
2. 优化方式：
    a. 每个字段优化建议需结合评分反馈点进行改写或补充。请按以下情况处理：
        i. 字段缺失 ➜ 补充内容或提示 “【请补充xxx】”。
        ii. 内容不规范 ➜ 重写为清晰、结构化的内容，
3. 优化内容：
    a. 参考规范模版进行修改，字段名格式必须是 `【步骤】` 这样的全角中括号，作为 JSON 的键名。
    b. 所有建议内容必须遵循下方“参考规范”标准。
    c. ** 若根据上一步评分反馈结果（stage1_json）判断需要优化【实际结果】、【预期结果】，你可以通过对比实际结果图和预期结果图的区别，先理解缺陷想表达的意思，再优化文字内容。**  
    d. ** 不需要解释截图中内容，请稍微概述缺陷本质然后直接提示用户“详见截图”或“具体内容见附件”。**
"""

YIYAO_SAAS_BUG_STANDARD = """
【前提条件】
    设备机型：若为小程序问题，则需要填写设备机型，否则不填写。
    登录账号/角色信息：
    问题发生时间：
【问题现象】必填
1、说明问题现象（可以是单个或多个现象描述，只要能清楚表述问题即可）
截图/录屏：

【重现步骤】
1、（描述操作过程，可以是单个步骤或多个步骤，只要能清楚表述问题复现过程即可）

【预期结果】必填
1、说明问题发生的预期结果（可以是单个或多个结果描述，只要能清楚表述预期即可）

【其他补充】（便于开发定位问题的一些其他信息，补充在这里）
    入口or菜单:
    日志信息：
    抓包信息：
"""

YIYAO_SAAS_POINT_BUG_STANDARD = """
1. 评分部分（dimension_scores）
    a. 格式要求：
        i. 每个维度评分范围为 `0.0 - 1.0`，必须为浮点数。
    b. 评分要求：
        i. 完整性：
            1. 问题现象、预期结果必填项，缺少填写一个字段扣0.4分。
                a. 若为小程序问题，【前提条件-设备机型】为必填项，缺少填写则扣0.4分，否则可不填写。
            2. 问题发生时间、重现步骤、入口or菜单、日志信息不作为评判要求，可不填写。
            3. 如果为后端问题，【其他补充-抓包信息】为空则扣0.4分。
            4. 如果为前端问题，没有截图或视频则扣0.4分。
            5. 【前提条件-登录角色/账号信息】以及【其他补充-抓包信息】都为空则扣0.4分。
        ii. 清晰度：出现歧义描述或逻辑跳跃扣0.4。
        iii. 相关性：出现明显无关内容扣0.4。
        iv. 详细程度：
            1. 若为前端问题，针对详细描述字段【问题现象】或【预期结果】的内容，先查看是否包含图片或者视频链接。
                a. 若仅贴视频链接没有文字，则扣0.4分。
                b. 若仅贴图片链接不包含文字则** 查看输入信息中图片链接对应的OCR解析的内容是否提到红框 **，若没有则扣0.4分。
    c. 注意事项：
        i. 只要段中含有简洁明了的文字描述（如“显示网络异常”、“按钮无反应”等），即认为已说明，无需长篇解释。只有在完全缺少文字，仅附图时，才视为“无说明”，不需要充分的文字说明!!
        ii. 若某项评分低于0.7，将触发人工复审，请确保低分有合理依据!! 
        iii. 若为后端问题则不要求有视频和截图。
    d. 字段内容判断规则
        i. 如果某字段下方紧跟内容，则视为该字段的内容，即使中间换行或为 JSON/截图。例如：【其他补充-抓包信息】\n{"header":{"version":"","flag":0},"body":{"seq":30,"cmd":"FaasGatewayServer/func/}，则视为该字段的内容。

2. 反馈部分（feedback）
    a. 格式要求：
        i. 所有纬度按照格式：`维度名 -扣分值：扣分原因`
    b. 注意事项：
        i. 如果某个纬度为满分，扣分为0，那么则不需要展示该纬度的反馈。
        ii. 扣分值需要和dimension_scores中的实际得分相匹配，例如完整性扣0.4分那么完整性得分为1 - 0.4 = 0.6分
   """

YIYAO_SAAS_OPTIMIZATION_STANDARD = """
1. 问题字段优化
    a. 你只需要优化“评分反馈结果（stage1_json）”中明确指出存在问题的字段。  
        i. 字段格式一般为：【字段名-子字段名】：扣分原因。
        ii. 如果反馈中没有提到某个字段存在问题，请不要对其进行任何优化或输出！
        iii. 例如：“问题发生时间”未在反馈中提及为问题字段，就不能出现在优化建议中！
   
2. 优化方式：
    a. 每个被指出的问题字段，需根据扣分原因进行内容补充或改写。请按以下情况处理：
        i. 字段缺失 ➜ 补充内容或提示 “【请补充xxx】”。
        ii. 内容不规范 ➜ 重写为清晰、结构化的内容。
3. 输出格式：
    a. 以 JSON 格式输出，键为全角中括号括起来的字段大类（如：【前提条件】）。
    b. 每个字段的值为字符串，按行列出有问题的子字段及优化建议。
    c. 如果没有需要优化的字段，请返回空字典：`{}`。

4. 补充说明：
    a. 不需要解释截图中内容，请稍微概述缺陷本质然后直接提示用户“详见截图”或“具体内容见附件”。
    b. 请不要进行“兜底补充”或“逻辑推断”，仅处理反馈中明确提到的问题字段！
"""


MIYING_BUG_STANDARD = """
【环境】
测试地址：https://test.pacs.qq.com

【前置条件】

【机型】

【步骤】
- 只要有内容即可，不要求非常详细。

【现象】
- 明确当前系统实际行为，禁止使用“异常”、“不正常”等模糊词  
✅ 示例：仍显示“已点赞”  
❌ 错误示例：显示异常  
【重现规律】

【预期结果】
- 明确说明期望出现的界面或行为结果  
✅ 示例：页面同步显示“已取消点赞”  
❌ 错误示例：正常展示  
"""

MIYING_POINT_BUG_STANDARD = """
1. 评分部分（dimension_scores）
    a. 格式要求：
        i. 每个维度评分范围为 `0.0 - 1.0`，必须为浮点数。
    b. 评分要求：
        i. 完整性：
            1. 环境、步骤、现象、预期结果为必填项，缺少填写一个字段扣0.4分。
            2. 前置条件、机型、重现规律不作为评分内容，可不填写。
        ii. 清晰度：出现歧义描述或逻辑跳跃扣0.4。
        iii. 相关性：出现明显无关内容扣0.4。
        iv. 详细程度：
            1. 针对详细描述字段【现象】或【预期结果】的内容，先查看是否包含图片或者视频链接。
                a. 若仅贴视频或图片链接并且没有文字说明，则扣0.4分。
    c. 注意事项：
        i. 只要段中含有简洁明了的文字描述（如“显示网络异常”、“按钮无反应”等），即认为已说明，无需长篇解释。只有在完全缺少文字，仅附图时，才视为“无说明”，不需要充分的文字说明!!
        ii. 若某项评分低于0.7，将触发人工复审，请确保低分有合理依据!! 

2. 反馈部分（feedback）
    a. 格式要求：
        i. 所有纬度按照格式：`维度名 -扣分值：扣分原因`
    b. 注意事项：
        i. 如果某个纬度为满分，扣分为0，那么则不需要展示该纬度的反馈。
        ii. 扣分值需要和dimension_scores中的实际得分相匹配，例如完整性扣0.4分那么完整性得分为1 - 0.4 = 0.6分
   """

MIYING_OPTIMIZATION_STANDARD = """
1. 问题字段优化:
    a. 你需要参考BUG规范模版中需要填写的字段，仅需针对上一步评分反馈结果（stage1_json）中扣分原因** 有问题的字段 **进行优化!
2. 优化方式：
    a. 每个字段优化建议需结合评分反馈点进行改写或补充。请按以下情况处理：
        i. 字段缺失 ➜ 补充内容或提示 “【请补充xxx】”。
        ii. 内容不规范 ➜ 重写为清晰、结构化的内容，
3. 优化内容：
    a. 参考规范模版进行修改，字段名格式必须是 `【步骤】` 这样的全角中括号，作为 JSON 的键名。
    b. 所有建议内容必须遵循“参考规范”标准。
    c. ** 若根据上一步评分反馈结果（stage1_json）判断需要优化【现象】、【预期结果】，你可以结合缺陷上下文及图片信息，先理解缺陷想表达的意思，再优化文字内容。**  
    d. ** 不需要解释截图中内容，请稍微概述缺陷本质然后直接提示用户“详见截图”或“具体内容见附件”。**
"""

DAOZHEN_BUG_STANDARD = """
【测试地址】
测试地址：https://test.pacs.qq.com

【机型】

【步骤】
- 描述操作过程，可以使用编号，操作对象需明确
- 可以是单个步骤或多个步骤，只要能清楚表述问题复现过程即可
- 不需要足够详细，只要能看懂即可
✅ 示例：
1. 打开App首页 -> 点击"我的"
或者：
1. 打开App首页 -> 点击“我的”
2. 点击头像 -> 进入编辑页
【现象】
- 明确当前系统实际行为，禁止使用“异常”、“不正常”等模糊词  
✅ 示例：仍显示“已点赞”  
❌ 错误示例：显示异常  
【重现规律】

【预期结果】
- 明确说明期望出现的界面或行为结果  
✅ 示例：页面同步显示“已取消点赞”  
❌ 错误示例：正常展示  
"""

DAOZHEN_POINT_BUG_STANDARD = """
1. 评分部分（dimension_scores）
    a. 格式要求：
        i. 每个维度评分范围为 `0.0 - 1.0`，必须为浮点数。
    b. 评分要求：
        i. 完整性：
            1. 环境、步骤、现象、预期结果为必填项，缺少填写一个字段扣0.4分。
            2. 机型、重现规律不作为评分内容，可不填写。
        ii. 清晰度：出现歧义描述或逻辑跳跃扣0.4。
        iii. 相关性：出现明显无关内容扣0.4。
        iv. 详细程度：
            1. 针对详细描述字段【现象】或【预期结果】的内容，先查看是否包含图片或者视频。
                a. 若仅贴视频或图片并且没有文字说明，则扣0.4分。
            2. 步骤要求只需要能看懂即可，不需要太过详细，可以是单个步骤或多个步骤。
    c. 注意事项：
        i. 只要段中含有简洁明了的文字描述（如“显示网络异常”、“按钮无反应”等），即认为已说明，无需长篇解释。只有在完全缺少文字，仅附图时，才视为“无说明”，不需要充分的文字说明!!
        ii. 若某项评分低于0.7，将触发人工复审，请确保低分有合理依据!! 

2. 反馈部分（feedback）
    a. 格式要求：
        i. 所有纬度按照格式：`维度名 -扣分值：扣分原因`
    b. 注意事项：
        i. 如果某个纬度为满分，扣分为0，那么则不需要展示该纬度的反馈。
        ii. 扣分值需要和dimension_scores中的实际得分相匹配，例如完整性扣0.4分那么完整性得分为1 - 0.4 = 0.6分
   """

DAOZHEN_OPTIMIZATION_STANDARD = """
1. 问题字段优化:
    a. 你需要参考BUG规范模版中需要填写的字段，仅需针对上一步评分反馈结果（stage1_json）中扣分原因** 有问题的字段 **进行优化!
2. 优化方式：
    a. 每个字段优化建议需结合评分反馈点进行改写或补充。请按以下情况处理：
        i. 字段缺失 ➜ 补充内容或提示 “【请补充xxx】”。
        ii. 内容不规范 ➜ 重写为清晰、结构化的内容，
3. 优化内容：
    a. 参考规范模版进行修改，字段名格式必须是 `【步骤】` 这样的全角中括号，作为 JSON 的键名。
    b. 所有建议内容必须遵循“参考规范”标准。
    c. ** 若根据上一步评分反馈结果（stage1_json）判断需要优化【现象】、【预期结果】，你可以结合缺陷上下文及图片信息，先理解缺陷想表达的意思，再优化文字内容。**  
    d. ** 不需要解释截图中内容，请稍微概述缺陷本质然后直接提示用户“详见截图”或“具体内容见附件”。**
"""

IMA_BUG_STANDARD = """
【安装包名】

【前置条件】

【机型】
设备机型必填

|测试机型|	ROM版本|
|

【步骤】
1. （描述操作过程，可以是单个步骤或多个步骤，只要能清楚表述问题复现过程即可）

【现象】
现象一定要有文字描述

【重现规律】

【预期结果】

【QIMEI36（上传日志/crash时必须提供）】

【bugly链接（可查到异常上报时提供）】

【备注】
"""

IMA_POINT_BUG_STANDARD = """
1. 评分部分（dimension_scores）
    a. 格式要求：
        i. 每个维度评分范围为 `0.0 - 1.0`，必须为浮点数。
    b. 评分要求：
        i. 完整性：
            1. 设备机型必填，缺少填写则扣0.4分。
            2. 【现象】必须有文字描述，缺少文字描述则扣0.4分。
            3. 问题大类区分前端还是后端：
                a. 样式类前端问题一定要有截图or录屏，缺少则扣0.4分。
                b. 后端问题一定要有设备ID，缺少则扣0.4分。
            4. 步骤、预期结果为必填项，缺少填写一个字段扣0.4分。步骤可以是单个或多个，只要能清楚表述问题复现过程即可。
            5. 安装包名、前置条件、重现规律、QIMEI36、bugly链接、备注不作为评判要求，可不填写。
        ii. 清晰度：出现歧义描述或逻辑跳跃扣0.4。
        iii. 相关性：出现明显无关内容扣0.4。
        iv. 详细程度：
            1. 针对详细描述字段【现象】或【预期结果】的内容，先查看是否包含图片或者视频链接。
                a. 若仅贴视频链接没有文字，则扣0.4分。
                b. 若仅贴图片链接不包含文字则** 查看输入信息中图片链接对应的OCR解析的内容是否提到红框 **，若没有则扣0.4分。
    c. 注意事项：
        i. 只要段中含有简洁明了的文字描述（如"显示网络异常"、"按钮无反应"等），即认为已说明，无需长篇解释。只有在完全缺少文字，仅附图时，才视为"无说明"，不需要充分的文字说明!!
        ii. 若某项评分低于0.7，将触发人工复审，请确保低分有合理依据!!
        iii. 若为后端问题则不要求有视频和截图，但需要设备ID。

2. 反馈部分（feedback）
    a. 格式要求：
        i. 所有纬度按照格式：`维度名 -扣分值：扣分原因`
    b. 注意事项：
        i. 如果某个纬度为满分，扣分为0，那么则不需要展示该纬度的反馈。
        ii. 扣分值需要和dimension_scores中的实际得分相匹配，例如完整性扣0.4分那么完整性得分为1 - 0.4 = 0.6分
   """

IMA_OPTIMIZATION_STANDARD = """
1. 问题字段优化:
    a. 你需要参考BUG规范模版中需要填写的字段，仅需针对上一步评分反馈结果（stage1_json）中扣分原因** 有问题的字段 **进行优化!
2. 优化方式：
    a. 每个字段优化建议需结合评分反馈点进行改写或补充。请按以下情况处理：
        i. 字段缺失 ➜ 补充内容或提示 "【请补充xxx】"。
        ii. 内容不规范 ➜ 重写为清晰、结构化的内容，
3. 优化内容：
    a. 参考规范模版进行修改，字段名格式必须是 `【步骤】` 这样的全角中括号，作为 JSON 的键名。
    b. 所有建议内容必须遵循"参考规范"标准。
    c. ** 若根据上一步评分反馈结果（stage1_json）判断需要优化【现象】、【预期结果】，你可以结合缺陷上下文及图片信息，先理解缺陷想表达的意思，再优化文字内容。**
    d. ** 不需要解释截图中内容，请稍微概述缺陷本质然后直接提示用户"详见截图"或"具体内容见附件"。**
"""

def get_point_bug_standard(workspace_id) -> str:
    if workspace_id in [YIYAO_SAAS_WORKSPACE_ID]:
        return YIYAO_SAAS_POINT_BUG_STANDARD
    elif workspace_id == MIYING_WORKSPACE_ID:
        return MIYING_POINT_BUG_STANDARD
    elif workspace_id == DAOZHEN_WORKSPACE_ID:
        return DAOZHEN_POINT_BUG_STANDARD
    elif workspace_id == IMA_WORKSPACE_ID:
        return IMA_POINT_BUG_STANDARD
    return JIANKANG_POINT_BUG_STANDARD


def get_stage1_prompt_example(workspace_id) -> dict:
    output_response1 = {
      "dimension_scores": {
      "完整性": 0.85,
      "清晰度": 0.8,
      "相关性": 0.95,
      "详细程度": 0.75
   },
      "feedback": "完整性 -0.15：缺少预期结果描述；清晰度 -0.2：步骤描述不够清晰；相关性 -0.05：内容偏离问题本身；详细程度 -0.1：未说明机型与系统版本",
   }
    output_response2 = {
        "dimension_scores": {
        "完整性": 0.6,
        "清晰度": 0.8,
        "相关性": 0.95,
        "详细程度": 0.6
    },
      "feedback": "完整性 -0.4：【前提条件-设备机型】：无机型相关信息，【其他补充-抓包信息】：缺失，后端问题必须要有抓包信息；清晰度 -0.2：【问题现象】：描述混乱；相关性 -0.05：内容偏离问题本身；详细程度 -0.4：【预期结果】：视频无文字描述，贴图未用红框标注且无文字描述",
    }
    output_response3 = {
        "dimension_scores": {
        "完整性": 0.6,
        "清晰度": 0.8,
        "相关性": 0.95,
        "详细程度": 0.6
    },
      "feedback": "完整性 -0.4：【步骤】：缺乏描述；清晰度 -0.2：【现象】：描述混乱；相关性 -0.05：内容偏离问题本身；详细程度 -0.4：【预期结果】：视频无文字描述",
    }
             
    if workspace_id in [JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID]:
        return output_response1
    elif workspace_id == YIYAO_SAAS_WORKSPACE_ID:
        return output_response2
    elif workspace_id in [MIYING_WORKSPACE_ID, DAOZHEN_WORKSPACE_ID]:
        return output_response3
    elif workspace_id == IMA_WORKSPACE_ID:
        return output_response3
    
    
def get_desc(workspace_id) -> dict:
    desc = {
      "完整性": "是否包含以下5个子字段内容：【条件】、【机型】、【步骤】、【预期结果】、【实际结果】，每个字段必须有实际内容，每缺少一个字段扣0.4分。步骤可以是单个或多个，只要能清楚表述问题复现过程即可",
      "清晰度": "语言是否表述清晰，无歧义或冗长跳跃，如出现语病、逻辑不通则扣0.4分",
      "相关性": "是否紧扣字段本身，不出现偏离问题本质的内容，如有跑题或与Bug无关的描述则扣0.4分",
      "详细程度": "操作步骤、期望结果、实际结果是否具体充分"
   }
    if workspace_id in [YIYAO_SAAS_WORKSPACE_ID]:
        desc = {
            "完整性": "设备机型、问题现象、预期结果是包含信息，账号、抓包信息二选一必填一个，样式前端问题一定要有截图，后端问题一定要有抓包信息，缺少一个字段扣0.4分",
            "清晰度": "言是否表述清晰，无歧义或冗长跳跃，如出现语病、逻辑不通则扣0.4分",
            "相关性": "是否紧扣字段本身，不出现偏离问题本质的内容，如有跑题或与Bug无关的描述则扣0.4分",
            "详细程度": "问题现象、预期结果是否具体充分"
        }
    elif workspace_id == MIYING_WORKSPACE_ID:
        desc = {
            "完整性": "环境、步骤、现象、预期结果为必填项，缺少一个字段扣0.4分。步骤可以是单个或多个，只要能清楚表述问题复现过程即可",
            "清晰度": "语言是否表述清晰，无歧义或冗长跳跃，如出现语病、逻辑不通则扣0.4分",
            "相关性": "是否紧扣字段本身，不出现偏离问题本质的内容，如有跑题或与Bug无关的描述则扣0.4分",
            "详细程度": "步骤、现象、预期结果是否具体充分"
        }
    elif workspace_id == DAOZHEN_WORKSPACE_ID:
        desc = {
            "完整性": "条件字段必须填写测试地址，步骤、现象、预期结构都为必填项，缺少一个字段扣0.4分。步骤可以是单个或多个，只要能清楚表述问题复现过程即可",
            "清晰度": "语言是否表述清晰，无歧义或冗长跳跃，如出现语病、逻辑不通则扣0.4分",
            "相关性": "是否紧扣字段本身，不出现偏离问题本质的内容，如有跑题或与Bug无关的描述则扣0.4分",
            "详细程度": "步骤、现象、预期结果是否具体充分"
        }
    elif workspace_id == IMA_WORKSPACE_ID:
        desc = {
            "完整性": "设备机型必填，现象必须有文字描述，步骤、预期结果为必填项，前端问题需要截图或录屏，后端问题需要设备ID，缺少一个必填项扣0.4分",
            "清晰度": "语言是否表述清晰，无歧义或冗长跳跃，如出现语病、逻辑不通则扣0.4分",
            "相关性": "是否紧扣字段本身，不出现偏离问题本质的内容，如有跑题或与Bug无关的描述则扣0.4分",
            "详细程度": "步骤、现象、预期结果是否具体充分"
        }
    return desc

def get_field_prompt_stage1(field_name: str, user_content, image_content, workspace_id) -> str:
    desc = get_desc(workspace_id)
    output_response = get_stage1_prompt_example(workspace_id)
    bug_standard = get_bug_report_standard(workspace_id).strip() 
    bug_point = get_point_bug_standard(workspace_id).strip()
    user_copy = copy.copy(user_content)
    description = user_copy.pop("详细描述", None)
    return f"""
# 🎯 背景 #
你是一位专业的缺陷质量评估专家，当前负责评估“{field_name}”字段的内容质量。你深入理解BUG规范性的知识，知道如何评估缺陷内容，判断其内容是否符合规范，并给出合理评分，下面是BUG规范性的参考规范模版：
=====
{bug_standard}
=====

# 📥 输入信息 #
1. 缺陷中详细描述字段如下，你需要基于此理解缺陷，并给出合理评分：
=====
{description}
=====

2. 缺陷中其它字段内容如下，你需要结合此理解缺陷，并给出合理评分：
=====
{json.dumps(user_copy, indent=4, ensure_ascii=False)}
=====

3. 如果详细描述字段中包含图片链接，下面是对截图内容的OCR解析说明，只是帮助你理解缺陷，不作为评分的内容：
=====
{json.dumps(image_content, ensure_ascii=False, indent=4)}
=====

# 评分纬度 # 
=====
{json.dumps(desc, indent=4, ensure_ascii=False)}
=====

# 🔧评分规则 #
=====
{bug_point}
=====

# 📤 输出格式要求 #
1. 请仅输出如下标准JSON结构,双引号不要单引号，** 不要附加解释或自然语言内容，严格遵循结构格式 **
2. 你只能参考示例的结构，不应直接使用示例的内容：

# 🧾输出示例 #
=====
{output_response}
=====
"""




def get_field_prompt_stage2_example(workspace_id: int) -> str:
    example_response = {}
    if workspace_id == JIANKANG_WORKSPACE_ID:
        example_response = {
            "suggest": {
                "【步骤】": "1. 打开腾讯健康小程序 -> 点击“健康问问” -> 点击“智能导诊”模块\n2. 选择年龄选项 -> 查看输入框位置",
                "【预期结果】": "输入框应固定显示，不遮挡选项内容"
            }
        }
    elif workspace_id == YIBAO_WORKSPACE_ID:
        example_response = {
            "suggest": {
                "【步骤】": "1. 打开医保小程序 -> 点击“医保查询” -> 选择“个人账户”\n2. 查看账户余额",
                "【预期结果】": "账户余额应正确显示，不出现负数"
            }
        }
    elif workspace_id == YIYAO_SAAS_WORKSPACE_ID:
        example_response = {
            "suggest": {
                "【前提条件】": "设备机型：【请填写具体的设备机型】\n登录账号/角色信息：【请填写登陆账号/角色信息】",
                "【问题现象】": "1、编辑页创建拜访成功，点击保存后没有直接跳转回详情页"
            }
        }
    elif workspace_id == MIYING_WORKSPACE_ID:
        example_response = {
            "suggest": {
                "【步骤】": "1、打开H5-切换到机构B-远程会诊-会诊详情页，会诊ID：10098937\n2、点击【查看影像】",
                "【现象】": "页面提示“暂无机构权限”（当前用户是医联体管理员）"
            }
        }
    elif workspace_id == DAOZHEN_WORKSPACE_ID:
        example_response = {
            "suggest": {
                "【步骤】": "1、打开链接，点击智能问答大模型，点击首页热门问题的修改\n2、删除所有的热门问题，查看页面，如图所示",
                "【预期结果】": "清空热门问题后的提示显示为中文"
            }
        }
    elif workspace_id == IMA_WORKSPACE_ID:
        example_response = {
            "suggest": {
                "【机型】": "iPhone 12 iOS 16.0",
                "【现象】": "点击登录按钮后页面无响应，详见截图"
            }
        }
    return example_response



def describe_optimization_function(workspace_id):
    if workspace_id in [JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID]:
        return JIANKANG_OPTIMIZATION_STANDARD
    elif workspace_id == YIYAO_SAAS_WORKSPACE_ID:
        return YIYAO_SAAS_OPTIMIZATION_STANDARD
    elif workspace_id == MIYING_WORKSPACE_ID:
        return MIYING_OPTIMIZATION_STANDARD
    elif workspace_id == DAOZHEN_WORKSPACE_ID:
        return DAOZHEN_OPTIMIZATION_STANDARD
    elif workspace_id == IMA_WORKSPACE_ID:
        return IMA_OPTIMIZATION_STANDARD

def get_field_prompt_stage2(field_name: str, user_content, stage1_json, image_content, workspace_id) -> str: 
    optimization_function = describe_optimization_function(workspace_id)
    example_response = get_field_prompt_stage2_example(workspace_id)
    user_content_copy = copy.copy(user_content)
    description = user_content_copy.pop("详细描述", None)
    return f"""
# 🎯 背景 #
你是一位专业的缺陷优化专家，专门负责优化低质量存在问题的缺陷，当前需要根据上一步对“{field_name}”字段的评分反馈结果进行**针对性优化**。你深入理解BUG规范性的知识，知道如何优化缺陷内容，使其符合规范。
下面是BUG规范性的参考规范模版：
=====
{get_bug_report_standard(workspace_id).strip()}
=====

# 📥 输入信息 #
1. 缺陷原始“详细描述”字段内容：
=====
{description}
=====

2. 缺陷其它相关字段，你可以基于此理解缺陷：
=====
{json.dumps(user_content_copy, indent=4, ensure_ascii=False)}
=====

3. 上一步评分反馈结果（stage1_json）
- 格式为：维度：扣分值：扣分原因。
- 你需要针对扣分原因中说明有问题的模板字段进行优化，没有特殊说明不足的字段不需要在建议中优化。
=====
{json.dumps(stage1_json, ensure_ascii=False, indent=4)}
=====

4. 若原文中有截图或视频链接，这里是OCR解析说明：
=====
{json.dumps(image_content, ensure_ascii=False, indent=4)}
=====

# 🧠 思维链 #
** 请遵循清晰的思维链进行处理：**
1. 分析评分反馈中指出的字段问题（缺失 or 不规范）。
2. 检查用户原始描述内容，结合 OCR 图像信息辅助理解。
3. 依据 bug 报告规范补足或重写字段内容。
4. 将优化结果仅填入 `suggest` 对应字段，输出为 JSON 格式。


# 🛠 优化任务说明 #
{optimization_function}

#📤 输出格式要求 #
1. 规范格式：
    a. 输出标准JSON格式：
        i. 外层字段为 `suggest`。
        ii. 内层仅包含需要优化的字段，字段标题为 key，优化内容为 value。
2. 特殊要求：
    a. 仅输出“评分反馈结果”中明确指出问题的字段，不得包含其他字段！
    b. 所有内容使用 **双引号**，不要使用单引号。
    c. **不要输出任何额外解释或自然语言说明**。

# 🧾 示例参考（内容请勿照搬）# 
{example_response}
"""


