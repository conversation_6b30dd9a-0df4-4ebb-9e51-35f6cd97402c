#!/usr/bin/env python3
"""
测试改进后的prompt效果
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath('.'))

from backend.app.service.bug_labeling.labeling_service import BugLabelingService

async def test_improved_prompt():
    """测试改进后的prompt"""
    print("="*60)
    print("测试改进后的Prompt效果")
    print("="*60)
    
    service = BugLabelingService()
    
    # 测试用例：你提到的问题场景
    test_cases = [
        {
            "name": "配置平台tips配置问题",
            "bug_data": {
                "ID": "test_001",
                "标题": "op配置平台健康问问组件tips配置不生效",
                "详细描述": "在op配置平台配置健康问问组件的tips的配置，结果没生效，用户看不到提示信息",
                "模块": "健康问问",
                "状态": "新建"
            },
            "expected_function": "健康问问组件/tips"
        },
        {
            "name": "智能导诊年龄选择问题", 
            "bug_data": {
                "ID": "test_002",
                "标题": "智能导诊页面年龄选择器无响应",
                "详细描述": "在智能导诊页面点击年龄选择器时没有任何反应，无法选择年龄进行下一步",
                "模块": "健康问问",
                "状态": "新建"
            },
            "expected_function": "智能导诊/年龄选择"
        },
        {
            "name": "页面滚动箭头问题",
            "bug_data": {
                "ID": "test_003", 
                "标题": "页面下滑箭头显示异常",
                "详细描述": "当用户滚动页面时，下滑箭头的显示状态不正确，有时该隐藏时仍然显示",
                "模块": "健康问问",
                "状态": "新建"
            },
            "expected_function": "下滑箭头"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试用例：{test_case['name']}")
        print("-" * 40)
        print(f"缺陷描述：{test_case['bug_data']['详细描述']}")
        print(f"期望功能标签：{test_case['expected_function']}")
        
        try:
            result = await service.label_bug(test_case['bug_data'])
            
            if result.error_message:
                print(f"❌ 打标失败：{result.error_message}")
                continue
            
            print(f"\n实际结果：")
            print(f"  问题类型：{result.problem_type}")
            print(f"  具体功能：{result.specific_function}")
            print(f"  置信度：{result.confidence:.2f}")
            print(f"  推理过程：{result.reasoning[:200]}...")
            
            # 检查是否符合期望
            if result.specific_function == test_case['expected_function']:
                print(f"✅ 功能标签正确匹配！")
            else:
                print(f"⚠️  功能标签不匹配")
                print(f"   期望：{test_case['expected_function']}")
                print(f"   实际：{result.specific_function}")
            
        except Exception as e:
            print(f"❌ 测试失败：{str(e)}")
    
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print("改进的Prompt特点：")
    print("1. 强调组件层面的功能识别")
    print("2. 提供具体的对比示例")
    print("3. 明确分析步骤和思路")
    print("4. 要求在推理过程中说明组件识别过程")
    print("\n如果结果仍不理想，可以进一步调整prompt中的示例和指导")

if __name__ == "__main__":
    asyncio.run(test_improved_prompt())
